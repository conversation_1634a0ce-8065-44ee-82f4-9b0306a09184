package com.example.mobsnap;

import net.minecraft.world.InteractionHand;
import net.minecraft.world.InteractionResult;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.entity.player.PlayerInteractEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

@Mod(MobSnap.MODID)
public class MobSnap {
    public static final String MODID = "mobsnap";

    public MobSnap() {
        MinecraftForge.EVENT_BUS.register(this);
    }

    @SubscribeEvent
    public void onEntityRightClick(PlayerInteractEvent.EntityInteract event) {
        Player player = event.getEntity();

        if (player.level().isClientSide()) return;
        if (event.getHand() != InteractionHand.MAIN_HAND) return;
        if (!player.getMainHandItem().isEmpty()) return;

        if (event.getTarget() instanceof LivingEntity living) {
            var tag = living.getPersistentData();
            int step = tag.contains("mobsnap_step") ? tag.getInt("mobsnap_step") : -1;
            step = (step + 1) % 4;
            tag.putInt("mobsnap_step", step);

            float yaw = step * 90f;
            living.setYRot(yaw);
            living.setYHeadRot(yaw);
            living.setYBodyRot(yaw);

            event.setCancellationResult(InteractionResult.SUCCESS);
            event.setCanceled(true);
        }
    }
}
